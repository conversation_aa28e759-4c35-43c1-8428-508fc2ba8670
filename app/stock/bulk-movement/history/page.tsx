"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import {
  ArrowLeft,
  Search,
  Package,
  ShoppingCart,
  Eye,
  Download,
  RefreshCw,
  Filter,
  ChevronLeft,
  ChevronRight,
  Trash2
} from "lucide-react"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { getVendors } from "@/lib/directus"
import type { Vendor } from "@/lib/directus"

interface BulkMovementItem {
  id: string
  product: {
    id: string
    name: string
    sku: string
    category: string
    unit: string
  }
  quantity: number
  quantity_converted?: number
  unit_used?: string
  base_unit_label?: string
  purchase_price: number
  total_cost: number
  notes?: string
  synced: boolean
  accurate_id?: string
  sync_error?: string
}

interface BulkMovement {
  reference_number: string
  title: string
  date: string
  type: "incoming" | "outgoing"
  vendor?: {
    id: string
    name: string
    code: string
  } | null
  items: BulkMovementItem[]
  total_items: number
  total_quantity: number
  total_cost: number
  created_at: string
  synced: boolean
  sync_errors: string[]
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function BulkMovementHistoryPage() {
  const router = useRouter()
  const { toast } = useToast()

  // State
  const [bulkMovements, setBulkMovements] = useState<any[]>([])
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // Filters
  const [search, setSearch] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [vendorFilter, setVendorFilter] = useState("all")
  const [dateFrom, setDateFrom] = useState("")
  const [dateTo, setDateTo] = useState("")
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())

  // Load vendors
  useEffect(() => {
    const loadVendors = async () => {
      try {
        const vendorData = await getVendors()
        setVendors(vendorData)
      } catch (error) {
        console.error("Error loading vendors:", error)
      }
    }
    loadVendors()
  }, [])

  // Load bulk movements
  const loadBulkMovements = async (page = 1) => {
    setIsLoading(true)
    try {
      // Build params with proper validation
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      })

      if (search) params.append('search', search)
      if (typeFilter !== "all") params.append('type', typeFilter)
      if (vendorFilter !== "all") params.append('vendor', vendorFilter)
      if (dateFrom) params.append('dateFrom', dateFrom)
      if (dateTo) params.append('dateTo', dateTo)

      // Debug logging for date filters
      console.log('📅 Frontend date filter debug:', {
        dateFrom,
        dateTo,
        dateFromValid: dateFrom ? !isNaN(new Date(dateFrom).getTime()) : null,
        dateToValid: dateTo ? !isNaN(new Date(dateTo).getTime()) : null,
        params: params.toString()
      })

      const token = localStorage.getItem('directus_token')
      const response = await fetch(`/api/stock/bulk-movement/history?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        })
        throw new Error(`Failed to fetch bulk movement history: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ API Response:', {
        success: result.success,
        dataCount: result.data?.length || 0,
        pagination: result.pagination,
        debug: result.debug,
        hasDateFilter: !!(dateFrom || dateTo)
      })

      if (result.success) {
        setBulkMovements(result.data)
        setPagination(result.pagination)

        // Enhanced logging for date filtering results
        if (dateFrom || dateTo) {
          console.log('📅 Date filter results:', {
            totalBulkMovements: result.data.length,
            dateRange: { from: dateFrom, to: dateTo },
            appliedFilters: result.debug?.appliedFilters,
            sampleMovements: result.data.slice(0, 3).map((movement: any) => ({
              reference: movement.reference_number,
              date: movement.date_key,
              title: movement.display_title
            }))
          })
        }

        // Show filter results in toast for user feedback
        if (dateFrom || dateTo || typeFilter !== "all" || vendorFilter !== "all" || search) {
          const activeFilters = []
          if (dateFrom || dateTo) activeFilters.push('Date')
          if (typeFilter !== "all") activeFilters.push('Type')
          if (vendorFilter !== "all") activeFilters.push('Vendor')
          if (search) activeFilters.push('Search')

          toast({
            title: "Filter Applied",
            description: `Found ${result.data.length} bulk movements with ${activeFilters.join(', ')} filter(s)`,
            variant: "default"
          })
        }
      } else {
        throw new Error(result.error || 'Unknown error occurred')
      }
    } catch (error) {
      console.error("❌ Error loading bulk movements:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load bulk movement history",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on mount and filter changes
  useEffect(() => {
    loadBulkMovements(1)
  }, [search, typeFilter, vendorFilter, dateFrom, dateTo])

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    loadBulkMovements(newPage)
  }

  // Toggle row expansion
  const toggleRowExpansion = (referenceNumber: string) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(referenceNumber)) {
      newExpanded.delete(referenceNumber)
    } else {
      newExpanded.add(referenceNumber)
    }
    setExpandedRows(newExpanded)
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  // Format date - fix timezone issue
  const formatDate = (dateString: string) => {
    // Parse date string directly without timezone conversion
    const [year, month, day] = dateString.split('-').map(Number)
    const localDate = new Date(year, month - 1, day) // month is 0-indexed
    return localDate.toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Handle delete bulk movement
  const handleDeleteBulkMovement = async (referenceNumber: string) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete bulk movement "${referenceNumber}"?\n\nThis will delete all stock movements in this bulk operation and recalculate current stock. This action cannot be undone.`
    )

    if (!confirmed) return

    try {
      const token = localStorage.getItem('directus_token')
      const response = await fetch('/api/stock/bulk-movement/history', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reference_number: referenceNumber })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: `Deleted ${result.deleted_count} movements from bulk operation "${referenceNumber}"`,
          variant: "default"
        })

        // Refresh the data
        loadBulkMovements(pagination.page)
      } else {
        throw new Error(result.error || 'Failed to delete bulk movement')
      }
    } catch (error) {
      console.error('❌ Error deleting bulk movement:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete bulk movement",
        variant: "destructive"
      })
    }
  }

  // Handle delete single item
  const handleDeleteSingleItem = async (itemId: string, referenceNumber: string) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete this item?\n\nThis will remove the item from the bulk movement and recalculate current stock. This action cannot be undone.`
    )

    if (!confirmed) return

    try {
      const token = localStorage.getItem('directus_token')

      // Use the bulk movement history API with single item deletion
      const response = await fetch('/api/stock/bulk-movement/history', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          movement_id: itemId,
          type: 'single_item'
        })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: `Item deleted successfully from bulk movement "${referenceNumber}"`,
          variant: "default"
        })

        // Refresh the data
        loadBulkMovements(pagination.page)
      } else {
        throw new Error(result.error || 'Failed to delete item')
      }
    } catch (error) {
      console.error('❌ Error deleting item:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete item",
        variant: "destructive"
      })
    }
  }

  // Handle manual sync for pending items
  const handleManualSync = async (itemId: string, referenceNumber: string) => {
    try {
      const token = localStorage.getItem('directus_token')

      const response = await fetch('/api/stock/bulk-movement/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          movement_id: itemId,
          type: 'single_item'
        })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: `Item synced successfully in bulk movement "${referenceNumber}"`,
          variant: "default"
        })

        // Refresh the data
        loadBulkMovements(pagination.page)
      } else {
        throw new Error(result.error || 'Failed to sync item')
      }
    } catch (error) {
      console.error('❌ Error syncing item:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to sync item",
        variant: "destructive"
      })
    }
  }

  // Handle manual sync for entire bulk movement
  const handleBulkSync = async (referenceNumber: string) => {
    try {
      const token = localStorage.getItem('directus_token')

      const response = await fetch('/api/stock/bulk-movement/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          reference_number: referenceNumber,
          type: 'bulk_movement'
        })
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: `Bulk movement "${referenceNumber}" synced successfully. ${result.synced_count} items synced.`,
          variant: "default"
        })

        // Refresh the data
        loadBulkMovements(pagination.page)
      } else {
        throw new Error(result.error || 'Failed to sync bulk movement')
      }
    } catch (error) {
      console.error('❌ Error syncing bulk movement:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to sync bulk movement",
        variant: "destructive"
      })
    }
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Bulk Movement History</h1>
            <p className="text-muted-foreground">
              View and manage bulk stock movement records
            </p>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <Label>Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search title, reference, product..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div className="space-y-2">
                <Label>Movement Type</Label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="incoming">Incoming</SelectItem>
                    <SelectItem value="outgoing">Outgoing</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Vendor Filter */}
              <div className="space-y-2">
                <Label>Vendor</Label>
                <Select value={vendorFilter} onValueChange={setVendorFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Vendors</SelectItem>
                    {vendors.map((vendor) => (
                      <SelectItem key={vendor.id} value={vendor.id.toString()}>
                        {vendor.vendor_name} ({vendor.vendor_code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="flex gap-2">
                  <Input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => {
                      console.log('📅 Date From changed:', e.target.value)
                      setDateFrom(e.target.value)
                    }}
                    placeholder="From"
                    title="Filter from this date"
                  />
                  <Input
                    type="date"
                    value={dateTo}
                    onChange={(e) => {
                      console.log('📅 Date To changed:', e.target.value)
                      setDateTo(e.target.value)
                    }}
                    placeholder="To"
                    title="Filter to this date"
                  />
                </div>
                {(dateFrom || dateTo) && (
                  <div className="text-xs text-muted-foreground">
                    {dateFrom && dateTo ? (
                      `Filtering from ${formatDate(dateFrom)} to ${formatDate(dateTo)}`
                    ) : dateFrom ? (
                      `Filtering from ${formatDate(dateFrom)}`
                    ) : (
                      `Filtering to ${formatDate(dateTo)}`
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('🧹 Clearing all filters')
                    setSearch("")
                    setTypeFilter("all")
                    setVendorFilter("all")
                    setDateFrom("")
                    setDateTo("")
                  }}
                  disabled={!search && typeFilter === "all" && vendorFilter === "all" && !dateFrom && !dateTo}
                >
                  Clear Filters
                </Button>
                {(search || typeFilter !== "all" || vendorFilter !== "all" || dateFrom || dateTo) && (
                  <div className="text-sm text-muted-foreground flex items-center">
                    Active filters: {[
                      search && "Search",
                      typeFilter !== "all" && "Type",
                      vendorFilter !== "all" && "Vendor",
                      (dateFrom || dateTo) && "Date"
                    ].filter(Boolean).join(", ")}
                  </div>
                )}
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  console.log('🔄 Refreshing data')
                  loadBulkMovements(pagination.page)
                }}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Bulk Movement Records</CardTitle>
                <CardDescription>
                  Found {pagination.total} bulk movement records
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]"></TableHead>
                        <TableHead>Reference & Title</TableHead>
                        <TableHead className="w-[120px]">Date</TableHead>
                        <TableHead className="w-[100px]">Type</TableHead>
                        <TableHead className="w-[150px]">Vendor</TableHead>
                        <TableHead className="w-[80px] text-right">Items</TableHead>
                        <TableHead className="w-[120px] text-right">Total Cost</TableHead>
                        <TableHead className="w-[100px]">Status</TableHead>
                        <TableHead className="w-[80px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {bulkMovements.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                            No bulk movements found
                          </TableCell>
                        </TableRow>
                      ) : (
                        bulkMovements.map((movement: any) => (
                          <React.Fragment key={movement.group_key}>
                            <TableRow
                              className="cursor-pointer hover:bg-muted/50"
                              onClick={() => toggleRowExpansion(movement.group_key)}
                            >
                              <TableCell>
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{movement.display_title || movement.title}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {movement.reference_number}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {movement.date_formatted}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={movement.type === "incoming" ? "default" : "secondary"}>
                                  {movement.type === "incoming" ? (
                                    <Package className="h-3 w-3 mr-1" />
                                  ) : (
                                    <ShoppingCart className="h-3 w-3 mr-1" />
                                  )}
                                  {movement.type}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {movement.vendor ? (
                                  <div>
                                    <div className="font-medium">{movement.vendor.name}</div>
                                    <div className="text-sm text-muted-foreground">
                                      {movement.vendor.code}
                                    </div>
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">-</span>
                                )}
                              </TableCell>
                              <TableCell className="text-right">{movement.total_items}</TableCell>
                              <TableCell className="text-right">
                                {formatCurrency(movement.total_cost)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={movement.synced ? "default" : "destructive"}>
                                  {movement.synced ? "Synced" : "Pending"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-1">
                                  {!movement.synced && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        handleBulkSync(movement.reference_number)
                                      }}
                                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                      title="Sync this bulk movement"
                                    >
                                      <RefreshCw className="h-4 w-4" />
                                    </Button>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleDeleteBulkMovement(movement.reference_number)
                                    }}
                                    className="text-destructive hover:text-destructive"
                                    title="Delete this bulk movement"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>

                            {/* Expanded row details */}
                            {expandedRows.has(movement.group_key) && (
                              <TableRow key={`${movement.group_key}-expanded`}>
                                <TableCell colSpan={9} className="bg-muted/20 p-0">
                                  <div className="p-6 space-y-4">
                                    <div className="flex items-center justify-between">
                                      <h4 className="font-medium text-lg">Items in this bulk movement:</h4>
                                      <Badge variant="outline" className="text-sm">
                                        {movement.items?.length || 0} items
                                      </Badge>
                                    </div>

                                    {movement.items && movement.items.length > 0 ? (
                                      <div className="rounded-lg border bg-background">
                                        <Table>
                                          <TableHeader>
                                            <TableRow className="bg-muted/50">
                                              <TableHead className="font-semibold">Product</TableHead>
                                              <TableHead className="text-right font-semibold">Quantity</TableHead>
                                              <TableHead className="text-right font-semibold">Unit Price</TableHead>
                                              <TableHead className="text-right font-semibold">Total</TableHead>
                                              <TableHead className="text-center font-semibold">Status</TableHead>
                                              <TableHead className="text-center font-semibold">Actions</TableHead>
                                            </TableRow>
                                          </TableHeader>
                                          <TableBody>
                                            {movement.items.map((item: any) => (
                                              <TableRow key={item.id} className="hover:bg-muted/30">
                                                <TableCell>
                                                  <div>
                                                    <div className="font-medium">{item.product.name}</div>
                                                    <div className="text-sm text-muted-foreground">
                                                      SKU: {item.product.sku} • {item.product.category}
                                                    </div>
                                                  </div>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                  <div className="font-medium">
                                                    {item.quantity_converted || item.quantity} {item.base_unit_label || item.product.unit}
                                                  </div>
                                                  {item.quantity_converted && item.quantity !== item.quantity_converted && (
                                                    <div className="text-xs text-muted-foreground">
                                                      Original: {item.quantity} {item.unit_used}
                                                    </div>
                                                  )}
                                                </TableCell>
                                                <TableCell className="text-right font-medium">
                                                  {formatCurrency(item.purchase_price)}
                                                </TableCell>
                                                <TableCell className="text-right font-medium">
                                                  {formatCurrency(item.total_cost)}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                  <Badge variant={item.synced ? "default" : "destructive"}>
                                                    {item.synced ? "✓ Synced" : "⏳ Pending"}
                                                  </Badge>
                                                  {item.sync_error && (
                                                    <div className="text-xs text-destructive mt-1">
                                                      Error: {item.sync_error}
                                                    </div>
                                                  )}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                  <div className="flex gap-1 justify-center">
                                                    {!item.synced && (
                                                      <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleManualSync(item.id, movement.reference_number)}
                                                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                                        title="Sync this item"
                                                      >
                                                        <RefreshCw className="h-4 w-4" />
                                                      </Button>
                                                    )}
                                                    <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      onClick={() => handleDeleteSingleItem(item.id, movement.reference_number)}
                                                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                                      title="Delete this item"
                                                    >
                                                      <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                  </div>
                                                </TableCell>
                                              </TableRow>
                                            ))}
                                          </TableBody>
                                        </Table>
                                      </div>
                                    ) : (
                                      <div className="text-center py-8 text-muted-foreground">
                                        No items found in this bulk movement
                                      </div>
                                    )}
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </React.Fragment>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-muted-foreground">
                      Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                      {pagination.total} results
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={!pagination.hasPrev}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      <span className="text-sm">
                        Page {pagination.page} of {pagination.totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={!pagination.hasNext}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  )
}
