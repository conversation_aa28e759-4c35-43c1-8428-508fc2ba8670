import { NextRequest, NextResponse } from 'next/server'
import { createDirectus, rest, readItems, updateItem } from '@directus/sdk'

const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL!
const DIRECTUS_TOKEN = process.env.DIRECTUS_TOKEN!

const directus = createDirectus(DIRECTUS_URL).with(rest())

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { movement_id, reference_number, type } = body

    console.log('🔄 Manual sync request:', { movement_id, reference_number, type })

    if (type === 'single_item' && movement_id) {
      // Sync single item
      const result = await syncSingleItem(movement_id)
      return NextResponse.json(result)
    } else if (type === 'bulk_movement' && reference_number) {
      // Sync entire bulk movement
      const result = await syncBulkMovement(reference_number)
      return NextResponse.json(result)
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid request parameters'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ Error in sync API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

async function syncSingleItem(movementId: string) {
  try {
    // Get the stock movement item
    const movements = await directus.request(
      readItems('StockMovement', {
        filter: { id: { _eq: movementId } },
        fields: ['*', { produk_gudang: ['*'] }]
      })
    )

    if (!movements || movements.length === 0) {
      throw new Error('Stock movement not found')
    }

    const movement = movements[0]

    // Check if already synced
    if (movement.synced) {
      return {
        success: true,
        message: 'Item is already synced',
        already_synced: true
      }
    }

    // Sync to Accurate (placeholder - implement actual sync logic)
    const syncResult = await syncToAccurate(movement)

    if (syncResult.success) {
      // Update the movement as synced
      await directus.request(
        updateItem('StockMovement', movementId, {
          synced: true,
          accurate_id: syncResult.accurate_id,
          sync_error: null
        })
      )

      return {
        success: true,
        message: 'Item synced successfully',
        accurate_id: syncResult.accurate_id
      }
    } else {
      // Update with sync error
      await directus.request(
        updateItem('StockMovement', movementId, {
          synced: false,
          sync_error: syncResult.error
        })
      )

      throw new Error(`Sync failed: ${syncResult.error}`)
    }

  } catch (error) {
    console.error('❌ Error syncing single item:', error)
    throw error
  }
}

async function syncBulkMovement(referenceNumber: string) {
  try {
    // Get all unsynced movements for this reference number
    const movements = await directus.request(
      readItems('StockMovement', {
        filter: {
          _and: [
            { reference_number: { _eq: referenceNumber } },
            { synced: { _eq: false } }
          ]
        },
        fields: ['*', { produk_gudang: ['*'] }]
      })
    )

    if (!movements || movements.length === 0) {
      return {
        success: true,
        message: 'All items in this bulk movement are already synced',
        synced_count: 0
      }
    }

    let syncedCount = 0
    const errors: string[] = []

    // Sync each movement
    for (const movement of movements) {
      try {
        const syncResult = await syncToAccurate(movement)

        if (syncResult.success) {
          await directus.request(
            updateItem('StockMovement', movement.id, {
              synced: true,
              accurate_id: syncResult.accurate_id,
              sync_error: null
            })
          )
          syncedCount++
        } else {
          await directus.request(
            updateItem('StockMovement', movement.id, {
              synced: false,
              sync_error: syncResult.error
            })
          )
          errors.push(`${movement.produk_gudang?.name || 'Unknown'}: ${syncResult.error}`)
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error'
        errors.push(`${movement.produk_gudang?.name || 'Unknown'}: ${errorMsg}`)
      }
    }

    return {
      success: syncedCount > 0,
      message: `Synced ${syncedCount} out of ${movements.length} items`,
      synced_count: syncedCount,
      total_items: movements.length,
      errors: errors.length > 0 ? errors : undefined
    }

  } catch (error) {
    console.error('❌ Error syncing bulk movement:', error)
    throw error
  }
}

// Placeholder function for Accurate sync
// Replace this with actual Accurate API integration
async function syncToAccurate(movement: any) {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // For now, just return success with a mock ID
    // In real implementation, this would call Accurate API
    const mockAccurateId = `ACC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log('📤 Syncing to Accurate (mock):', {
      movement_id: movement.id,
      product: movement.produk_gudang?.name,
      quantity: movement.quantity,
      type: movement.type,
      mock_accurate_id: mockAccurateId
    })

    return {
      success: true,
      accurate_id: mockAccurateId
    }

  } catch (error) {
    console.error('❌ Error syncing to Accurate:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Sync failed'
    }
  }
}
